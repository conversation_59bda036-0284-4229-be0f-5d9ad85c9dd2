import { json } from '@sveltejs/kit';
import { supabase } from '$lib/supabase.js';

// CORS headers
const corsHeaders = {
	'Access-Control-Allow-Origin': '*',
	'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
	'Access-Control-Allow-Headers': 'Content-Type, Authorization',
	'Access-Control-Max-Age': '86400'
};

export async function POST({ request }) {
	try {
		const body = await request.json();
		
		// Validate the request body structure
		if (!body.type || (body.type !== 'exact_match' && body.type !== 'episode_range')) {
			return json(
				{ error: 'Invalid type. Must be "exact_match" or "episode_range"' },
				{ status: 400, headers: corsHeaders }
			);
		}

		let insertData = {};

		if (body.type === 'exact_match') {
			// For exact_match, the body should contain title mappings
			const titleOverride = { ...body };
			delete titleOverride.type; // Remove the type field
			
			if (Object.keys(titleOverride).length === 0) {
				return json(
					{ error: 'No title override data provided' },
					{ status: 400, headers: corsHeaders }
				);
			}

			insertData = {
				title_override: titleOverride,
				episode_overrides: null
			};
		} else if (body.type === 'episode_range') {
			// For episode_range, validate required fields
			const requiredFields = ['dest_title', 'source_title', 'dest_episode_start', 'dest_episode_end', 'source_episode_start', 'source_episode_end'];
			const episodeData = { ...body };
			delete episodeData.type; // Remove the type field

			for (const field of requiredFields) {
				if (!(field in episodeData)) {
					return json(
						{ error: `Missing required field: ${field}` },
						{ status: 400, headers: corsHeaders }
					);
				}
			}

			insertData = {
				title_override: null,
				episode_overrides: episodeData
			};
		}

		// Insert into Supabase
		const { data, error } = await supabase
			.from('MoeDownloader')
			.insert([insertData])
			.select();

		if (error) {
			console.error('Supabase error:', error);
			return json(
				{ error: 'Failed to insert data into database' },
				{ status: 500, headers: corsHeaders }
			);
		}

		return json({ success: true, data: data[0] }, { status: 201, headers: corsHeaders });
	} catch (error) {
		console.error('Server error:', error);
		return json(
			{ error: 'Internal server error' },
			{ status: 500, headers: corsHeaders }
		);
	}
}

export async function GET() {
	try {
		const { data, error } = await supabase
			.from('MoeDownloader')
			.select('*')
			.order('id', { ascending: false });

		if (error) {
			console.error('Supabase error:', error);
			return json(
				{ error: 'Failed to fetch data from database' },
				{ status: 500, headers: corsHeaders }
			);
		}

		return json({ data }, { status: 200, headers: corsHeaders });
	} catch (error) {
		console.error('Server error:', error);
		return json(
			{ error: 'Internal server error' },
			{ status: 500, headers: corsHeaders }
		);
	}
}

// Handle CORS preflight requests
export async function OPTIONS() {
	return new Response(null, {
		status: 200,
		headers: corsHeaders
	});
}
