<script>
	import '../../app.css';
	
	let selectedType = 'exact_match';
	let titleMappings = [{ original: '', override: '' }];
	let episodeData = {
		source_title: '',
		dest_title: '',
		description: '',
		source_episode_start: 1,
		source_episode_end: 1,
		dest_episode_start: 1,
		dest_episode_end: 1
	};
	let submitting = false;
	let result = null;
	let error = null;

	function addTitleMapping() {
		titleMappings = [...titleMappings, { original: '', override: '' }];
	}

	function removeTitleMapping(index) {
		titleMappings = titleMappings.filter((_, i) => i !== index);
	}

	async function submitForm() {
		submitting = true;
		error = null;
		result = null;

		try {
			let requestBody = { type: selectedType };

			if (selectedType === 'exact_match') {
				// Build title override object
				const titleOverride = {};
				for (const mapping of titleMappings) {
					if (mapping.original && mapping.override) {
						titleOverride[mapping.original] = mapping.override;
					}
				}
				
				if (Object.keys(titleOverride).length === 0) {
					error = 'Please provide at least one title mapping';
					submitting = false;
					return;
				}

				requestBody = { ...requestBody, ...titleOverride };
			} else {
				// Validate episode data
				if (!episodeData.source_title || !episodeData.dest_title) {
					error = 'Source title and destination title are required';
					submitting = false;
					return;
				}

				requestBody = { ...requestBody, ...episodeData };
			}

			const response = await fetch('/api/moedownloader', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(requestBody)
			});

			const responseData = await response.json();

			if (response.ok) {
				result = responseData;
				// Reset form
				if (selectedType === 'exact_match') {
					titleMappings = [{ original: '', override: '' }];
				} else {
					episodeData = {
						source_title: '',
						dest_title: '',
						description: '',
						source_episode_start: 1,
						source_episode_end: 1,
						dest_episode_start: 1,
						dest_episode_end: 1
					};
				}
			} else {
				error = responseData.error || 'Failed to submit data';
			}
		} catch (err) {
			error = 'Network error: ' + err.message;
		} finally {
			submitting = false;
		}
	}
</script>

<div class="container mx-auto p-6 max-w-4xl">
	<div class="mb-6">
		<a href="/" class="text-blue-600 hover:text-blue-800">← Back to List</a>
	</div>

	<h1 class="text-3xl font-bold mb-6">Add MoeDownloader Configuration</h1>

	<div class="bg-white shadow-lg rounded-lg p-6">
		<form on:submit|preventDefault={submitForm}>
			<!-- Type Selection -->
			<div class="mb-6">
				<label class="block text-sm font-medium text-gray-700 mb-2">Configuration Type</label>
				<div class="space-y-2">
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={selectedType}
							value="exact_match"
							class="mr-2"
						/>
						<span>Title Override (Exact Match)</span>
					</label>
					<label class="flex items-center">
						<input
							type="radio"
							bind:group={selectedType}
							value="episode_range"
							class="mr-2"
						/>
						<span>Episode Range Override</span>
					</label>
				</div>
			</div>

			{#if selectedType === 'exact_match'}
				<!-- Title Override Form -->
				<div class="mb-6">
					<h3 class="text-lg font-medium mb-4">Title Mappings</h3>
					{#each titleMappings as mapping, index}
						<div class="flex gap-4 mb-4 items-end">
							<div class="flex-1">
								<label class="block text-sm font-medium text-gray-700 mb-1">Original Title</label>
								<input
									type="text"
									bind:value={mapping.original}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder="e.g., Grand Blue S2"
								/>
							</div>
							<div class="flex-1">
								<label class="block text-sm font-medium text-gray-700 mb-1">Override Title</label>
								<input
									type="text"
									bind:value={mapping.override}
									class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
									placeholder="e.g., Grand Blue Season 2"
								/>
							</div>
							{#if titleMappings.length > 1}
								<button
									type="button"
									on:click={() => removeTitleMapping(index)}
									class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md"
								>
									Remove
								</button>
							{/if}
						</div>
					{/each}
					<button
						type="button"
						on:click={addTitleMapping}
						class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md"
					>
						Add Another Mapping
					</button>
				</div>
			{:else}
				<!-- Episode Override Form -->
				<div class="mb-6">
					<h3 class="text-lg font-medium mb-4">Episode Range Configuration</h3>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Source Title</label>
							<input
								type="text"
								bind:value={episodeData.source_title}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								placeholder="e.g., Dainanaoji"
								required
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Destination Title</label>
							<input
								type="text"
								bind:value={episodeData.dest_title}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								placeholder="e.g., Tensei shitara Dai nana Ouji..."
								required
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Source Episode Start</label>
							<input
								type="number"
								bind:value={episodeData.source_episode_start}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								min="1"
								required
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Source Episode End</label>
							<input
								type="number"
								bind:value={episodeData.source_episode_end}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								min="1"
								required
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Destination Episode Start</label>
							<input
								type="number"
								bind:value={episodeData.dest_episode_start}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								min="1"
								required
							/>
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">Destination Episode End</label>
							<input
								type="number"
								bind:value={episodeData.dest_episode_end}
								class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								min="1"
								required
							/>
						</div>
					</div>
					<div class="mt-4">
						<label class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
						<textarea
							bind:value={episodeData.description}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							rows="3"
							placeholder="Optional description..."
						></textarea>
					</div>
				</div>
			{/if}

			<!-- Submit Button -->
			<div class="flex justify-end">
				<button
					type="submit"
					disabled={submitting}
					class="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-2 rounded-md"
				>
					{submitting ? 'Submitting...' : 'Submit Configuration'}
				</button>
			</div>
		</form>

		<!-- Results -->
		{#if result}
			<div class="mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
				<strong>Success!</strong> Configuration added successfully.
				<pre class="mt-2 text-sm">{JSON.stringify(result.data, null, 2)}</pre>
			</div>
		{/if}

		{#if error}
			<div class="mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
				<strong>Error:</strong> {error}
			</div>
		{/if}
	</div>
</div>
