<script>
	import '../app.css';
	import { onMount } from 'svelte';

	let data = [];
	let loading = true;
	let error = null;
	let showRawJson = {};

	onMount(async () => {
		try {
			const response = await fetch('/api/moedownloader');
			const result = await response.json();

			if (response.ok) {
				data = result.data;
			} else {
				error = result.error || 'Failed to fetch data';
			}
		} catch (err) {
			error = 'Network error: ' + err.message;
		} finally {
			loading = false;
		}
	});

	function toggleRawJson(id) {
		showRawJson[id] = !showRawJson[id];
	}

	function formatTitleOverride(titleOverride) {
		if (!titleOverride) return null;
		return Object.entries(titleOverride).map(([key, value]) => ({
			original: key,
			override: value
		}));
	}

	function formatEpisodeOverride(episodeOverride) {
		if (!episodeOverride) return null;
		return {
			sourceTitle: episodeOverride.source_title,
			destTitle: episodeOverride.dest_title,
			description: episodeOverride.description || 'No description',
			sourceEpisodeRange: `${episodeOverride.source_episode_start} - ${episodeOverride.source_episode_end}`,
			destEpisodeRange: `${episodeOverride.dest_episode_start} - ${episodeOverride.dest_episode_end}`
		};
	}
</script>

<div class="container mx-auto p-6">
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-3xl font-bold">MoeDownloader Configuration</h1>
		<a
			href="/add"
			class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
		>
			Add New Configuration
		</a>
	</div>

	{#if loading}
		<div class="flex justify-center items-center py-8">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
			<span class="ml-2">Loading...</span>
		</div>
	{:else if error}
		<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
			<strong>Error:</strong> {error}
		</div>
	{:else}
		<div class="space-y-6">
			{#each data as item (item.id)}
				<div class="bg-white shadow-lg rounded-lg p-6 border">
					<div class="flex justify-between items-start mb-4">
						<h2 class="text-xl font-semibold">Entry #{item.id}</h2>
						<button
							on:click={() => toggleRawJson(item.id)}
							class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
						>
							{showRawJson[item.id] ? 'Hide' : 'Show'} Raw JSON
						</button>
					</div>

					{#if item.title_override}
						<div class="mb-4">
							<h3 class="text-lg font-medium text-blue-600 mb-2">Title Override</h3>
							<div class="bg-blue-50 p-4 rounded">
								{#each formatTitleOverride(item.title_override) as mapping}
									<div class="mb-2">
										<span class="font-medium">Original:</span>
										<span class="bg-gray-200 px-2 py-1 rounded text-sm">{mapping.original}</span>
										<span class="mx-2">→</span>
										<span class="font-medium">Override:</span>
										<span class="bg-green-200 px-2 py-1 rounded text-sm">{mapping.override}</span>
									</div>
								{/each}
							</div>
						</div>
					{/if}

					{#if item.episode_overrides}
						{@const episodeData = formatEpisodeOverride(item.episode_overrides)}
						<div class="mb-4">
							<h3 class="text-lg font-medium text-purple-600 mb-2">Episode Override</h3>
							<div class="bg-purple-50 p-4 rounded">
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<span class="font-medium">Source Title:</span>
										<div class="bg-gray-200 px-2 py-1 rounded text-sm mt-1">{episodeData.sourceTitle}</div>
									</div>
									<div>
										<span class="font-medium">Destination Title:</span>
										<div class="bg-green-200 px-2 py-1 rounded text-sm mt-1">{episodeData.destTitle}</div>
									</div>
									<div>
										<span class="font-medium">Source Episodes:</span>
										<div class="bg-blue-200 px-2 py-1 rounded text-sm mt-1">{episodeData.sourceEpisodeRange}</div>
									</div>
									<div>
										<span class="font-medium">Destination Episodes:</span>
										<div class="bg-blue-200 px-2 py-1 rounded text-sm mt-1">{episodeData.destEpisodeRange}</div>
									</div>
								</div>
								{#if episodeData.description !== 'No description'}
									<div class="mt-4">
										<span class="font-medium">Description:</span>
										<div class="bg-yellow-100 px-2 py-1 rounded text-sm mt-1">{episodeData.description}</div>
									</div>
								{/if}
							</div>
						</div>
					{/if}

					{#if showRawJson[item.id]}
						<div class="mt-4">
							<h3 class="text-lg font-medium text-gray-600 mb-2">Raw JSON Data</h3>
							<pre class="bg-gray-100 p-4 rounded overflow-x-auto text-sm"><code>{JSON.stringify(item, null, 2)}</code></pre>
						</div>
					{/if}
				</div>
			{/each}

			{#if data.length === 0}
				<div class="text-center py-8 text-gray-500">
					<p>No data found in the MoeDownloader table.</p>
				</div>
			{/if}
		</div>
	{/if}
</div>
